import os
import uuid
import qrcode
from io import BytesIO
import base64
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, make_response, current_app, send_file
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, TextAreaField, SubmitField, SelectField, HiddenField
from wtforms.validators import DataRequired, Length, Optional

from db import db
from models import Weapon, Personnel, Warehouse, WeaponTransaction, MaintenanceRecord, ActivityLog

# Create the blueprint
weapons_bp = Blueprint('weapons', __name__, url_prefix='/weapons')

# Configuration for file uploads
UPLOAD_FOLDER = 'static/uploads/weapon_documents'
ALLOWED_EXTENSIONS = {'pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'}
MAX_FILE_SIZE = 25 * 1024 * 1024  # 25 MB

def safe_float_convert(value):
    """تحويل آمن للقيم إلى float مع التعامل مع القيم الفارغة والرموز"""
    if not value:
        return None
    if isinstance(value, str):
        value = value.strip()
        if not value or value == '-' or value == '':
            return None
        try:
            return float(value)
        except ValueError:
            return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension"""
    if not filename or '.' not in filename:
        return False

    file_extension = filename.rsplit('.', 1)[1].lower()
    return file_extension in ALLOWED_EXTENSIONS

def save_weapon_document(file, weapon_serial):
    """Save uploaded weapon document and return the file path"""
    if file and file.filename and allowed_file(file.filename):
        # Check file size
        file.seek(0, 2)  # Seek to end of file
        file_size = file.tell()
        file.seek(0)  # Reset to beginning

        if file_size == 0:
            print("File is empty")
            return None

        if file_size > MAX_FILE_SIZE:
            print(f"File too large: {file_size} bytes")
            return None

        # Create upload directory if it doesn't exist
        upload_dir = os.path.join(current_app.root_path, UPLOAD_FOLDER)
        os.makedirs(upload_dir, exist_ok=True)

        # Generate secure filename
        filename = secure_filename(file.filename)

        # Check if filename has extension
        if '.' not in filename:
            return None

        file_extension = filename.rsplit('.', 1)[1].lower()

        # Validate file extension
        if file_extension not in ALLOWED_EXTENSIONS:
            return None

        new_filename = f"weapon_{weapon_serial}_{uuid.uuid4().hex[:8]}.{file_extension}"

        file_path = os.path.join(upload_dir, new_filename)

        try:
            file.save(file_path)
            # Return relative path for database storage
            return os.path.join(UPLOAD_FOLDER, new_filename).replace('\\', '/')
        except Exception as e:
            print(f"Error saving file: {e}")
            return None
    return None

# Forms
class WeaponForm(FlaskForm):
    storage_number = StringField('رقم الحفظ', validators=[Length(max=100)])
    serial_number = StringField('الرقم التسلسلي', validators=[DataRequired(), Length(min=1, max=100)])
    rifle_number = StringField('رقم البندقية', validators=[Length(max=100)])
    rifle_type = StringField('نوع البندقية', validators=[Length(max=100)])
    pistol_number = StringField('رقم المسدس', validators=[Length(max=100)])
    pistol_type = StringField('نوع المسدس', validators=[Length(max=100)])
    status = SelectField('حالة السلاح', choices=[
        ('نشط', 'نشط'),
        ('إجازة', 'إجازة'),
        ('مهمة', 'مهمة'),
        ('صيانة', 'صيانة'),
        ('دورة', 'دورة'),
        ('شاغر', 'شاغر'),
        ('مستلم', 'مستلم'),
        ('رماية', 'رماية'),
        ('أخرى', 'أخرى')
    ])
    condition = StringField('الحالة', validators=[Length(max=100)])
    notes = TextAreaField('ملاحظات')
    weapon_document = FileField('سند السلاح', validators=[
        FileAllowed(['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'], 'يُسمح فقط بملفات PDF, صور, أو مستندات Word!')
    ])
    warehouse_id = SelectField('المستودع', coerce=int)
    submit = SubmitField('حفظ')

    def __init__(self, *args, **kwargs):
        super(WeaponForm, self).__init__(*args, **kwargs)
        # Only show warehouses that the current user has access to
        if current_user.is_authenticated:
            if current_user.is_admin_role:
                self.warehouse_id.choices = [(w.id, w.name) for w in Warehouse.query.all()]
            else:
                self.warehouse_id.choices = [(w.id, w.name) for w in current_user.warehouses]

class WeaponTransferForm(FlaskForm):
    weapon_id = HiddenField('معرف السلاح')
    target_warehouse_id = SelectField('المستودع الهدف', coerce=int)
    notes = TextAreaField('ملاحظات')
    submit = SubmitField('نقل')

    def __init__(self, *args, **kwargs):
        super(WeaponTransferForm, self).__init__(*args, **kwargs)
        # Only show warehouses that the current user has access to
        if current_user.is_authenticated:
            if current_user.is_admin_role:
                self.target_warehouse_id.choices = [(w.id, w.name) for w in Warehouse.query.all()]
            else:
                self.target_warehouse_id.choices = [(w.id, w.name) for w in current_user.warehouses]

class WeaponCheckoutForm(FlaskForm):
    weapon_id = HiddenField('معرف السلاح')
    personnel_id = SelectField('الجندي', coerce=int)
    notes = TextAreaField('ملاحظات')
    submit = SubmitField('تسليم')

    def __init__(self, *args, **kwargs):
        super(WeaponCheckoutForm, self).__init__(*args, **kwargs)
        # Get the warehouse ID from the form or URL parameter
        warehouse_id = kwargs.get('warehouse_id', None)
        if warehouse_id:
            self.personnel_id.choices = [(p.id, f"{p.name} ({p.personnel_id})")
                                         for p in Personnel.query.filter_by(warehouse_id=warehouse_id).all()]

class WeaponReturnForm(FlaskForm):
    transaction_id = HiddenField('معرف العملية')
    condition = StringField('حالة السلاح')
    notes = TextAreaField('ملاحظات')
    submit = SubmitField('استلام')

class MaintenanceRecordForm(FlaskForm):
    weapon_id = HiddenField('معرف السلاح')
    maintenance_type = SelectField('نوع الصيانة', choices=[
        ('routine', 'صيانة دورية'),
        ('repair', 'إصلاح'),
        ('upgrade', 'تحديث'),
        ('inspection', 'فحص'),
        ('replacement', 'طلب تعويض'),
        ('other', 'أخرى')
    ])
    description = TextAreaField('وصف الصيانة', validators=[DataRequired()])
    start_date = StringField('تاريخ البدء', validators=[DataRequired()])
    end_date = StringField('تاريخ الانتهاء (متوقع)', validators=[Optional()])
    status = SelectField('الحالة', choices=[
        ('ongoing', 'جارية'),
        ('completed', 'مكتملة'),
        ('cancelled', 'ملغاة')
    ])
    cost = StringField('التكلفة')
    notes = TextAreaField('ملاحظات')
    submit = SubmitField('حفظ')

# Helper function to generate QR code for weapons (offline-compatible)
def generate_qr_code(data):
    try:
        # Try to use qrcode with PIL
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_H,
            box_size=15,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        buffered = BytesIO()
        img.save(buffered, format="PNG", quality=95)
        img_str = base64.b64encode(buffered.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"
    except ImportError:
        # Fallback: return data for JavaScript QR generation
        return data

# Routes
@weapons_bp.route('/')
@login_required
def index():
    # منع مناوب السرية من الوصول لصفحة الأسلحة
    if current_user.is_company_duty:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('receipts.index'))
    # Get weapons from warehouses the user has access to
    weapons = []
    if current_user.is_admin_role:
        weapons = Weapon.query.all()
    else:
        for warehouse in current_user.warehouses:
            weapons.extend(Weapon.query.filter_by(warehouse_id=warehouse.id).all())

    # Count weapons by status
    status_counts = {
        'نشط': 0,
        'إجازة': 0,
        'مهمة': 0,
        'صيانة': 0,
        'دورة': 0,
        'شاغر': 0,
        'مستلم': 0,
        'رماية': 0,
        'أخرى': 0
    }

    for weapon in weapons:
        if weapon.status in status_counts:
            status_counts[weapon.status] += 1
        else:
            status_counts['أخرى'] += 1

    return render_template('weapons/index.html',
                          weapons=weapons,
                          status_counts=status_counts,
                          title='قائمة الأسلحة')

@weapons_bp.route('/warehouse/<int:warehouse_id>')
@login_required
def warehouse_weapons(warehouse_id):
    # Check if user has access to this warehouse
    warehouse = Warehouse.query.get_or_404(warehouse_id)
    if not current_user.is_admin_role and warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('weapons.index'))

    weapons = Weapon.query.filter_by(warehouse_id=warehouse_id).all()

    # Count weapons by status
    status_counts = {
        'نشط': 0,
        'إجازة': 0,
        'مهمة': 0,
        'صيانة': 0,
        'دورة': 0,
        'شاغر': 0,
        'مستلم': 0,
        'رماية': 0,
        'أخرى': 0
    }

    for weapon in weapons:
        if weapon.status in status_counts:
            status_counts[weapon.status] += 1
        else:
            status_counts['أخرى'] += 1

    return render_template('weapons/index.html',
                          weapons=weapons,
                          status_counts=status_counts,
                          warehouse=warehouse,
                          title=f'أسلحة {warehouse.name}')

@weapons_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لإضافة أسلحة جديدة', 'danger')
        return redirect(url_for('weapons.index'))

    form = WeaponForm()
    if form.validate_on_submit():
        # Check if serial number already exists
        existing_weapon = Weapon.query.filter_by(serial_number=form.serial_number.data).first()
        if existing_weapon:
            flash('الرقم التسلسلي موجود بالفعل', 'danger')
            return render_template('weapons/create.html', form=form, title='إضافة سلاح جديد')

        # Generate barcode and QR code
        # Use serial number for barcode instead of random UUID
        barcode = form.serial_number.data
        qr_data = f"Serial: {form.serial_number.data}, Rifle: {form.rifle_number.data or '-'}, Pistol: {form.pistol_number.data or '-'}"
        qr_code = f"WPN-QR-{uuid.uuid4().hex[:12].upper()}"

        # Handle weapon document upload
        weapon_document_path = None
        if form.weapon_document.data:
            weapon_document_path = save_weapon_document(form.weapon_document.data, form.serial_number.data)
            if not weapon_document_path:
                flash('فشل في رفع ملف سند السلاح. تأكد من أن الملف من النوع المسموح (PDF, JPG, PNG, DOC, DOCX) وأن حجمه أقل من 25 ميجابايت.', 'warning')

        # Create new weapon
        weapon = Weapon(
            weapon_number=form.storage_number.data,
            serial_number=form.serial_number.data,
            name=f"بندقية: {form.rifle_type.data or '-'}, مسدس: {form.pistol_type.data or '-'}",
            type=f"بندقية: {form.rifle_number.data or '-'}, مسدس: {form.pistol_number.data or '-'}",
            status=form.status.data,
            condition=form.condition.data,
            notes=form.notes.data,
            barcode=barcode,
            qr_code=qr_code,
            weapon_document=weapon_document_path,
            warehouse_id=form.warehouse_id.data
        )
        db.session.add(weapon)

        # Log the weapon creation
        warehouse = Warehouse.query.get(form.warehouse_id.data)
        log = ActivityLog(
            action="إضافة سلاح",
            description=f"تم إضافة سلاح جديد: {weapon.name} (الرقم التسلسلي: {weapon.serial_number})",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=warehouse.id
        )
        db.session.add(log)

        db.session.commit()
        flash('تم إضافة السلاح بنجاح!', 'success')
        return redirect(url_for('weapons.index'))

    return render_template('weapons/create.html', form=form, title='إضافة سلاح جديد')

@weapons_bp.route('/<int:weapon_id>')
@login_required
def details(weapon_id):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا السلاح', 'danger')
        return redirect(url_for('weapons.index'))

    # QR code will be generated by JavaScript on the client side
    qr_data = f"Serial: {weapon.serial_number}, Type: {weapon.type}, Status: {weapon.status}"
    qr_code_image = None  # Not needed for client-side generation

    # Get weapon history (transactions and maintenance)
    transactions = WeaponTransaction.query.filter_by(weapon_id=weapon.id).order_by(WeaponTransaction.timestamp.desc()).all()
    maintenance_records = MaintenanceRecord.query.filter_by(weapon_id=weapon.id).order_by(MaintenanceRecord.start_date.desc()).all()

    # Initialize forms for actions
    transfer_form = WeaponTransferForm(weapon_id=weapon.id)
    checkout_form = WeaponCheckoutForm(weapon_id=weapon.id, warehouse_id=weapon.warehouse_id)
    maintenance_form = MaintenanceRecordForm(weapon_id=weapon.id)
    return_form = WeaponReturnForm()  # Initialize return form

    # Find active transactions for this weapon (checkouts without returns)
    active_transactions = [t for t in transactions if t.transaction_type == 'checkout']
    type_translation = {
        'pistol': 'مسدس',
        'rifle': 'بندقية',
        'sniper': 'قناص',
        'machine_gun': 'رشاش',
        'other': 'أخرى'
    }
    weapon.type = type_translation.get(weapon.type, weapon.type)
    from utils import translate_maintenance_type, translate_maintenance_status

    return render_template('weapons/details.html',
                          weapon=weapon,
                          qr_code=qr_code_image,
                          transactions=transactions,
                          active_transactions=active_transactions,
                          maintenance_records=maintenance_records,
                          transfer_form=transfer_form,
                          checkout_form=checkout_form,
                          return_form=return_form,
                          maintenance_form=maintenance_form,
                          translate_maintenance_type=translate_maintenance_type,
                          translate_maintenance_status=translate_maintenance_status,
                          title=f'تفاصيل السلاح: {weapon.name}')

@weapons_bp.route('/<int:weapon_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(weapon_id):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('weapons.index'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لتعديل هذا السلاح', 'danger')
        return redirect(url_for('weapons.index'))

    # استخراج بيانات البندقية والمسدس من حقول النوع والاسم
    rifle_number = ""
    pistol_number = ""
    rifle_type = ""
    pistol_type = ""

    # استخراج أرقام البندقية والمسدس من حقل النوع
    if weapon.type:
        type_parts = weapon.type.split(',')
        for part in type_parts:
            if 'بندقية:' in part:
                rifle_number = part.replace('بندقية:', '').strip()
                if rifle_number == '-':
                    rifle_number = ""
            elif 'مسدس:' in part:
                pistol_number = part.replace('مسدس:', '').strip()
                if pistol_number == '-':
                    pistol_number = ""

    # استخراج أنواع البندقية والمسدس من حقل الاسم
    if weapon.name:
        name_parts = weapon.name.split(',')
        for part in name_parts:
            if 'بندقية:' in part:
                rifle_type = part.replace('بندقية:', '').strip()
                if rifle_type == '-':
                    rifle_type = ""
            elif 'مسدس:' in part:
                pistol_type = part.replace('مسدس:', '').strip()
                if pistol_type == '-':
                    pistol_type = ""

    # تهيئة النموذج مع البيانات المستخرجة
    form = WeaponForm()

    # تعيين البيانات الأولية للنموذج
    if request.method == 'GET':
        form.storage_number.data = weapon.weapon_number
        form.serial_number.data = weapon.serial_number
        form.rifle_number.data = rifle_number
        form.pistol_number.data = pistol_number
        form.rifle_type.data = rifle_type
        form.pistol_type.data = pistol_type
        form.status.data = weapon.status
        form.condition.data = weapon.condition
        form.notes.data = weapon.notes
        form.warehouse_id.data = weapon.warehouse_id

    if form.validate_on_submit():
        # Check if serial number already exists (excluding this weapon)
        existing_weapon = Weapon.query.filter(
            Weapon.serial_number == form.serial_number.data,
            Weapon.id != weapon.id
        ).first()

        if existing_weapon:
            flash('الرقم التسلسلي موجود بالفعل', 'danger')
            return render_template('weapons/edit.html', form=form, weapon=weapon, title='تعديل سلاح')

        # تسجيل البيانات قبل التعديل للتشخيص
        print("=== بيانات السلاح قبل التعديل ===")
        print(f"رقم الحفظ: {weapon.weapon_number}")
        print(f"الرقم التسلسلي: {weapon.serial_number}")
        print(f"الاسم: {weapon.name}")
        print(f"النوع: {weapon.type}")
        print(f"الحالة: {weapon.status}")
        print(f"الحالة الفنية: {weapon.condition}")
        print(f"المستودع: {weapon.warehouse_id}")
        print(f"الملاحظات: {weapon.notes}")

        # تسجيل البيانات المرسلة من النموذج للتشخيص
        print("=== بيانات النموذج المرسلة ===")
        print(f"رقم الحفظ: {form.storage_number.data}")
        print(f"الرقم التسلسلي: {form.serial_number.data}")
        print(f"رقم البندقية: {form.rifle_number.data}")
        print(f"نوع البندقية: {form.rifle_type.data}")
        print(f"رقم المسدس: {form.pistol_number.data}")
        print(f"نوع المسدس: {form.pistol_type.data}")
        print(f"الحالة: {form.status.data}")
        print(f"الحالة الفنية: {form.condition.data}")
        print(f"المستودع: {form.warehouse_id.data}")
        print(f"الملاحظات: {form.notes.data}")

        # Handle weapon document upload
        if form.weapon_document.data:
            weapon_document_path = save_weapon_document(form.weapon_document.data, form.serial_number.data)
            if weapon_document_path:
                weapon.weapon_document = weapon_document_path
            else:
                flash('فشل في رفع ملف سند السلاح. تأكد من أن الملف من النوع المسموح (PDF, JPG, PNG, DOC, DOCX) وأن حجمه أقل من 25 ميجابايت.', 'warning')

        # Update weapon data
        weapon.weapon_number = form.storage_number.data
        weapon.serial_number = form.serial_number.data
        weapon.name = f"بندقية: {form.rifle_type.data or '-'}, مسدس: {form.pistol_type.data or '-'}"
        weapon.type = f"بندقية: {form.rifle_number.data or '-'}, مسدس: {form.pistol_number.data or '-'}"
        weapon.status = form.status.data
        weapon.condition = form.condition.data
        weapon.notes = form.notes.data

        # Check if warehouse changed
        if weapon.warehouse_id != form.warehouse_id.data:
            old_warehouse = weapon.warehouse
            new_warehouse = Warehouse.query.get(form.warehouse_id.data)

            # Create a transfer transaction
            transaction = WeaponTransaction(
                transaction_type='transfer',
                weapon_id=weapon.id,
                personnel_id=None,  # System transfer doesn't need a personnel record
                source_warehouse_id=old_warehouse.id,
                target_warehouse_id=new_warehouse.id,
                user_id=current_user.id,
                notes=f"تم نقل السلاح من {old_warehouse.name} إلى {new_warehouse.name} عن طريق تعديل بيانات السلاح"
            )
            db.session.add(transaction)

            # Update weapon warehouse
            weapon.warehouse_id = form.warehouse_id.data

        # Log the weapon update
        log = ActivityLog(
            action="تعديل سلاح",
            description=f"تم تعديل بيانات السلاح: {weapon.name} (الرقم التسلسلي: {weapon.serial_number})",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=weapon.warehouse_id
        )
        db.session.add(log)

        try:
            db.session.commit()

            # تسجيل البيانات بعد التعديل والحفظ للتشخيص
            # إعادة تحميل السلاح من قاعدة البيانات للتأكد من حفظ البيانات
            updated_weapon = Weapon.query.get(weapon.id)
            print("=== بيانات السلاح بعد التعديل والحفظ ===")
            print(f"رقم الحفظ: {updated_weapon.weapon_number}")
            print(f"الرقم التسلسلي: {updated_weapon.serial_number}")
            print(f"الاسم: {updated_weapon.name}")
            print(f"النوع: {updated_weapon.type}")
            print(f"الحالة: {updated_weapon.status}")
            print(f"الحالة الفنية: {updated_weapon.condition}")
            print(f"المستودع: {updated_weapon.warehouse_id}")
            print(f"سند السلاح: {updated_weapon.weapon_document}")
            print(f"الملاحظات: {updated_weapon.notes}")

            flash('تم تحديث بيانات السلاح بنجاح!', 'success')
        except Exception as e:
            db.session.rollback()
            print(f"=== خطأ في حفظ البيانات ===")
            print(f"الخطأ: {str(e)}")
            flash(f'حدث خطأ أثناء حفظ البيانات: {str(e)}', 'danger')

        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    return render_template('weapons/edit.html', form=form, weapon=weapon, title='تعديل سلاح')

# @weapons_bp.route('/<int:weapon_id>/delete', methods=['POST'])
# @login_required
# def delete(weapon_id):
#     weapon = Weapon.query.get_or_404(weapon_id)

#     # Check if user has access to the warehouse and proper role
#     if weapon.warehouse not in current_user.warehouses or not current_user.is_admin:
#         flash('ليس لديك صلاحية لحذف هذا السلاح', 'danger')
#         return redirect(url_for('weapons.index'))

#     # Check for related maintenance records
#     if weapon.maintenance_records:  # assuming you have a relationship: weapon.maintenance_records
#         print("=====================")
#         print(weapon.maintenance_records)
#         print("=====================")
#         flash('لا يمكن حذف السلاح لوجود سجلات صيانة مرتبطة به.', 'danger')
#         return redirect(url_for('weapons.index'))

#     try:
#         weapon_name = weapon.name
#         serial_number = weapon.serial_number

#         # Log the weapon deletion
#         log = ActivityLog(
#             action="حذف سلاح",
#             description=f"تم حذف السلاح: {weapon_name} (الرقم التسلسلي: {serial_number}) بواسطة {current_user.username}",
#             ip_address=request.remote_addr,
#             user_id=current_user.id,
#             warehouse_id=weapon.warehouse_id
#         )
#         db.session.add(log)

#         # Delete the weapon
#         db.session.delete(weapon)
#         db.session.commit()

#         flash(f'تم حذف السلاح: {weapon_name} (الرقم التسلسلي: {serial_number})', 'success')
#     except Exception as e:
#         db.session.rollback()
#         flash(f'حدث خطأ أثناء محاولة حذف السلاح: {str(e)}', 'danger')

#     return redirect(url_for('weapons.index'))

@weapons_bp.route('/<int:weapon_id>/delete', methods=['POST'])
@login_required
def delete(weapon_id):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('weapons.index'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لحذف هذا السلاح', 'danger')
        return redirect(url_for('weapons.index'))

    try:
        weapon_name = weapon.name
        serial_number = weapon.serial_number

        # Delete related transactions
        for transaction in weapon.transactions.all():
            db.session.delete(transaction)

        # Delete related maintenance records
        for record in weapon.maintenance_records.all():
            db.session.delete(record)

        # Delete related audit items
        for audit in weapon.audit_items.all():
            db.session.delete(audit)

        # Log the weapon deletion
        log = ActivityLog(
            action="حذف سلاح",
            description=f"تم حذف السلاح: {weapon_name} (الرقم التسلسلي: {serial_number}) بواسطة {current_user.username}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=weapon.warehouse_id
        )
        db.session.add(log)

        # Delete the weapon itself
        db.session.delete(weapon)
        db.session.commit()

        flash(f'تم حذف السلاح: {weapon_name} (الرقم التسلسلي: {serial_number})', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء محاولة حذف السلاح: {str(e)}', 'danger')

    return redirect(url_for('weapons.index'))


@weapons_bp.route('/<int:weapon_id>/transfer', methods=['POST'])
@login_required
def transfer(weapon_id):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لنقل هذا السلاح', 'danger')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    form = WeaponTransferForm()
    if form.validate_on_submit():
        target_warehouse = Warehouse.query.get(form.target_warehouse_id.data)

        # Make sure target warehouse is different from current warehouse
        if weapon.warehouse_id == target_warehouse.id:
            flash('لا يمكن نقل السلاح إلى نفس المستودع', 'warning')
            return redirect(url_for('weapons.details', weapon_id=weapon.id))

        # Create transaction record
        transaction = WeaponTransaction(
            transaction_type='transfer',
            weapon_id=weapon.id,
            personnel_id=None,  # System transfer doesn't need a personnel record
            source_warehouse_id=weapon.warehouse_id,
            target_warehouse_id=target_warehouse.id,
            user_id=current_user.id,
            notes=form.notes.data
        )
        db.session.add(transaction)

        # Update weapon warehouse
        source_warehouse = weapon.warehouse
        weapon.warehouse_id = target_warehouse.id

        # Get personnel associated with the weapon
        personnel_name = None
        if weapon.personnel:
            for person in weapon.personnel:
                personnel_name = person.name
                break

        # Log the weapon transfer with improved description
        log = ActivityLog(
            action="نقل سلاح",
            description=f"تم نقل السلاح{' الخاص بـ ' + personnel_name if personnel_name else ''} من {source_warehouse.name} إلى {target_warehouse.name}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=source_warehouse.id
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم نقل السلاح إلى {target_warehouse.name} بنجاح!', 'success')

    return redirect(url_for('weapons.details', weapon_id=weapon.id))

@weapons_bp.route('/<int:weapon_id>/checkout', methods=['POST'])
@login_required
def checkout(weapon_id):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لتسليم هذا السلاح', 'danger')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    form = WeaponCheckoutForm()
    form.personnel_id.choices = [(p.id, f"{p.name} ({p.personnel_id})")
                                for p in Personnel.query.filter_by(warehouse_id=weapon.warehouse_id).all()]

    if form.validate_on_submit():
        personnel = Personnel.query.get(form.personnel_id.data)

        # Create transaction record
        transaction = WeaponTransaction(
            transaction_type='checkout',
            weapon_id=weapon.id,
            personnel_id=personnel.id,
            source_warehouse_id=weapon.warehouse_id,
            user_id=current_user.id,
            notes=form.notes.data
        )
        db.session.add(transaction)

        # Update weapon status to assigned
        weapon.status = 'نشط'

        # Associate weapon with personnel
        if weapon.type in ['pistol', 'مسدس']:
            # This is a pistol, set as primary or secondary based on what they already have
            has_pistol = False
            for w in personnel.weapons:
                if w.type in ['pistol', 'مسدس']:
                    has_pistol = True
                    break

            if not has_pistol:
                # Associate as primary pistol
                personnel.weapons.append(weapon)
        elif weapon.type in ['rifle', 'بندقية', 'sniper', 'قناص', 'machine_gun', 'رشاش']:
            # This is a rifle, set as primary or secondary based on what they already have
            has_rifle = False
            for w in personnel.weapons:
                if w.type in ['rifle', 'بندقية', 'sniper', 'قناص', 'machine_gun', 'رشاش']:
                    has_rifle = True
                    break

            if not has_rifle:
                # Associate as primary rifle
                personnel.weapons.append(weapon)
        else:
            # Other weapon type, just associate
            personnel.weapons.append(weapon)

        # Log the weapon checkout with improved description
        log = ActivityLog(
            action="تسليم سلاح",
            description=f"تم تسليم السلاح: {weapon.name} (الرقم التسلسلي: {weapon.serial_number}) إلى {personnel.name}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=weapon.warehouse_id
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم تسليم السلاح إلى {personnel.name} بنجاح!', 'success')

    return redirect(url_for('weapons.details', weapon_id=weapon.id))

@weapons_bp.route('/<int:weapon_id>/return/<int:transaction_id>', methods=['GET', 'POST'])
@login_required
def return_weapon(weapon_id, transaction_id):
    weapon = Weapon.query.get_or_404(weapon_id)
    transaction = WeaponTransaction.query.get_or_404(transaction_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لاستلام هذا السلاح', 'danger')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    form = WeaponReturnForm()
    if form.validate_on_submit():
        personnel = transaction.personnel

        # Create return transaction record
        return_transaction = WeaponTransaction(
            transaction_type='return',
            weapon_id=weapon.id,
            personnel_id=personnel.id,
            source_warehouse_id=weapon.warehouse_id,
            user_id=current_user.id,
            notes=form.notes.data
        )
        db.session.add(return_transaction)

        # Update weapon condition if provided
        if form.condition.data:
            weapon.condition = form.condition.data

        # Remove weapon association from personnel
        if weapon in personnel.weapons:
            personnel.weapons.remove(weapon)

        # Log the weapon return with improved description
        log = ActivityLog(
            action="استلام سلاح",
            description=f"تم استلام السلاح: {weapon.name} (الرقم التسلسلي: {weapon.serial_number}) من {personnel.name}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=weapon.warehouse_id
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم استلام السلاح من {personnel.name} بنجاح!', 'success')

    return redirect(url_for('weapons.details', weapon_id=weapon.id))

@weapons_bp.route('/<int:weapon_id>/maintenance', methods=['POST'])
@login_required
def add_maintenance(weapon_id):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لإضافة سجل صيانة لهذا السلاح', 'danger')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    form = MaintenanceRecordForm()
    if form.validate_on_submit():
        # Parse dates
        try:
            start_date = datetime.strptime(form.start_date.data, '%Y-%m-%d')
            end_date = datetime.strptime(form.end_date.data, '%Y-%m-%d') if form.end_date.data else None
        except ValueError:
            flash('صيغة التاريخ غير صحيحة، استخدم الصيغة YYYY-MM-DD', 'danger')
            return redirect(url_for('weapons.details', weapon_id=weapon.id))

        # Create maintenance record
        maintenance = MaintenanceRecord(
            weapon_id=weapon.id,
            maintenance_type=form.maintenance_type.data,
            description=form.description.data,
            start_date=start_date,
            end_date=end_date,
            status=form.status.data,
            cost=safe_float_convert(form.cost.data),
            notes=form.notes.data,
            user_id=current_user.id
        )
        db.session.add(maintenance)

        # If status is ongoing, update weapon status to maintenance
        if form.status.data == 'ongoing':
            weapon.status = 'صيانة'

        # Log the maintenance record creation
        log = ActivityLog(
            action="إضافة سجل صيانة",
            description=f"تم إضافة سجل صيانة للسلاح: {weapon.name} (الرقم التسلسلي: {weapon.serial_number})",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=weapon.warehouse_id
        )
        db.session.add(log)

        db.session.commit()
        flash('تم إضافة سجل الصيانة بنجاح!', 'success')

    return redirect(url_for('weapons.details', weapon_id=weapon.id))

@weapons_bp.route('/<int:weapon_id>/status/<status>')
@login_required
def update_status(weapon_id, status):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('weapons.index'))

    # Check if user has proper role
    if not (current_user.is_admin_role or current_user.is_warehouse_manager or current_user.is_inventory_manager):
        flash('ليس لديك صلاحية لتغيير حالة هذا السلاح', 'danger')
        return redirect(url_for('weapons.index'))

    # Update weapon status
    old_status = weapon.status
    weapon.status = status

    # Get personnel associated with the weapon
    personnel_name = None
    if weapon.personnel:
        for person in weapon.personnel:
            personnel_name = person.name
            break

    # Log the status update with improved description
    log = ActivityLog(
        action="تغيير حالة سلاح",
        description=f"تم تغيير حالة السلاح{' للفرد ' + personnel_name if personnel_name else ''}: {weapon.name} (الرقم التسلسلي: {weapon.serial_number}) من {old_status} إلى {status}",
        ip_address=request.remote_addr,
        user_id=current_user.id,
        warehouse_id=weapon.warehouse_id
    )
    db.session.add(log)

    db.session.commit()
    flash(f'تم تغيير حالة السلاح إلى {status} بنجاح!', 'success')

    return redirect(url_for('weapons.details', weapon_id=weapon.id))

@weapons_bp.route('/<int:weapon_id>/qr-code')
@login_required
def get_qr_code(weapon_id):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا السلاح', 'danger')
        return redirect(url_for('weapons.index'))

    # Check if download parameter is provided
    download = request.args.get('download', 0, type=int)
    inline = request.args.get('inline', 0, type=int)

    # QR code will be generated by JavaScript on the client side
    qr_data = f"Serial: {weapon.serial_number}, Name: {weapon.name}, Type: {weapon.type}, Warehouse: {weapon.warehouse.name}"

    if download:
        # For download, we need to generate QR code on server
        # But since PIL is not available, return a simple text file instead
        response = make_response(qr_data)
        response.headers["Content-Disposition"] = f"attachment; filename={weapon.serial_number}_qr.txt"
        response.headers["Content-Type"] = "text/plain"
        return response

    if inline:
        # Return just the data for JavaScript generation
        return qr_data

    return render_template('weapons/qr_code.html',
                         weapon=weapon,
                         title=f'رمز QR للسلاح: {weapon.name}')

@weapons_bp.route('/<int:weapon_id>/download-document')
@login_required
def download_document(weapon_id):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا السلاح', 'danger')
        return redirect(url_for('weapons.index'))

    # Check if weapon has a document
    if not weapon.weapon_document:
        flash('لا يوجد ملف مرفق لهذا السلاح', 'warning')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    # Build file path
    file_path = os.path.join(current_app.root_path, weapon.weapon_document)

    # Check if file exists
    if not os.path.exists(file_path):
        flash('الملف المرفق غير موجود', 'danger')
        return redirect(url_for('weapons.details', weapon_id=weapon.id))

    # Get file extension for content type
    file_extension = weapon.weapon_document.rsplit('.', 1)[1].lower()
    content_type = {
        'pdf': 'application/pdf',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    }.get(file_extension, 'application/octet-stream')

    # Generate filename for download
    filename = f"weapon_document_{weapon.serial_number}.{file_extension}"

    return send_file(file_path,
                    as_attachment=True,
                    download_name=filename,
                    mimetype=content_type)

@weapons_bp.route('/<int:weapon_id>/barcode')
@login_required
def barcode(weapon_id):
    weapon = Weapon.query.get_or_404(weapon_id)

    # Check if user has access to the warehouse
    if not current_user.is_admin_role and weapon.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا السلاح', 'danger')
        return redirect(url_for('weapons.index'))

    # Check if download parameter is provided
    download = request.args.get('download', 0, type=int)
    inline = request.args.get('inline', 0, type=int)

    # Barcode will be generated by JavaScript on the client side
    barcode_data = weapon.serial_number

    if download:
        # For download, return a simple text file instead
        response = make_response(barcode_data)
        response.headers["Content-Disposition"] = f"attachment; filename={weapon.serial_number}_barcode.txt"
        response.headers["Content-Type"] = "text/plain"
        return response

    if inline:
        # Return just the data for JavaScript generation
        return barcode_data

    return render_template('weapons/barcode.html',
                         weapon=weapon,
                         title=f'باركود للسلاح: {weapon.name}')

@weapons_bp.route('/search')
@login_required
def search():
    query = request.args.get('q', '')
    if not query:
        return redirect(url_for('weapons.index'))

    # Get user's accessible warehouses
    if current_user.is_admin_role:
        # Admin can see all weapons
        weapons = Weapon.query.filter(
            (Weapon.weapon_number.ilike(f'%{query}%') |
             Weapon.serial_number.ilike(f'%{query}%') |
             Weapon.name.ilike(f'%{query}%') |
             Weapon.type.ilike(f'%{query}%'))
        ).all()
    else:
        # Regular users can only see weapons from their warehouses
        warehouse_ids = [w.id for w in current_user.warehouses]
        weapons = Weapon.query.filter(
            Weapon.warehouse_id.in_(warehouse_ids),
            (Weapon.weapon_number.ilike(f'%{query}%') |
             Weapon.serial_number.ilike(f'%{query}%') |
             Weapon.name.ilike(f'%{query}%') |
             Weapon.type.ilike(f'%{query}%'))
        ).all()

    return render_template('weapons/search_results.html',
                          weapons=weapons,
                          query=query,
                          title='نتائج البحث')
